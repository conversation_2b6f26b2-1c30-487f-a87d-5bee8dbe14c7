# ERA AI Agent Environment Configuration

# LM Studio Configuration
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=local-model

# Ollama Configuration (for embeddings)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# Database Configuration
DATABASE_PATH=era_agent.db

# ERA Syntax Data Path (adjust to your actual path)
ERA_SYNTAX_DATA_PATH=F:/limiya/ERA語法資料

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=era_agent.log

# Generation Configuration
DEFAULT_MAX_TOKENS=4096
DEFAULT_TEMPERATURE=0.7
DEFAULT_TOP_P=0.9

# Output Configuration
DEFAULT_OUTPUT_PATH=./generated_games
DEFAULT_ENCODING=utf-8-sig

# Agent Configuration
ENABLE_RAG=true
ENABLE_MCP_TOOLS=true
ENABLE_VALIDATION=true

# Optional: Model-specific settings
# If you have multiple models configured
MAIN_AGENT_MODEL=lm_studio_default
ERB_SCRIPT_AGENT_MODEL=lm_studio_default
CSV_DATA_AGENT_MODEL=lm_studio_default
SYSTEM_FLOW_AGENT_MODEL=lm_studio_default
CHARACTER_AGENT_MODEL=lm_studio_default
GAME_LOGIC_AGENT_MODEL=lm_studio_default

# Performance Settings
MAX_CONCURRENT_AGENTS=3
AGENT_TIMEOUT=300
RAG_TOP_K=5
EMBEDDING_BATCH_SIZE=10

# Development Settings
DEBUG_MODE=false
SAVE_INTERMEDIATE_RESULTS=true
ENABLE_CACHE=true