"""
MCP (Model Context Protocol) 工具调用接口
支持ERA游戏生成过程中的各种工具调用
"""

import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import inspect

class ToolType(Enum):
    FILE_OPERATION = "file_operation"
    ERA_SYNTAX = "era_syntax" 
    DATA_QUERY = "data_query"
    VALIDATION = "validation"
    GENERATION = "generation"

@dataclass
class ToolParameter:
    """工具参数定义"""
    name: str
    type: str  # string, integer, boolean, array, object
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[Any]] = None

@dataclass 
class ToolDefinition:
    """工具定义"""
    name: str
    description: str
    category: ToolType
    parameters: List[ToolParameter]
    function: Callable
    async_function: bool = False

class MCPToolManager:
    """MCP工具管理器"""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.logger = logging.getLogger(__name__)
        
    def register_tool(self, 
                     name: str,
                     description: str,
                     category: ToolType,
                     parameters: List[ToolParameter],
                     function: Callable,
                     async_function: bool = False):
        """注册工具"""
        tool = ToolDefinition(
            name=name,
            description=description,
            category=category,
            parameters=parameters,
            function=function,
            async_function=async_function
        )
        
        self.tools[name] = tool
        self.logger.info(f\"Registered tool: {name}\")\n        \n    def get_tool_schema(self, tool_name: str) -> Dict[str, Any]:\n        \"\"\"获取工具schema\"\"\"\n        if tool_name not in self.tools:\n            return None\n            \n        tool = self.tools[tool_name]\n        \n        schema = {\n            \"name\": tool.name,\n            \"description\": tool.description,\n            \"category\": tool.category.value,\n            \"parameters\": {\n                \"type\": \"object\",\n                \"properties\": {},\n                \"required\": []\n            }\n        }\n        \n        for param in tool.parameters:\n            schema[\"parameters\"][\"properties\"][param.name] = {\n                \"type\": param.type,\n                \"description\": param.description\n            }\n            \n            if param.enum:\n                schema[\"parameters\"][\"properties\"][param.name][\"enum\"] = param.enum\n                \n            if param.default is not None:\n                schema[\"parameters\"][\"properties\"][param.name][\"default\"] = param.default\n                \n            if param.required:\n                schema[\"parameters\"][\"required\"].append(param.name)\n                \n        return schema\n        \n    def get_all_schemas(self) -> List[Dict[str, Any]]:\n        \"\"\"获取所有工具schema\"\"\"\n        return [self.get_tool_schema(name) for name in self.tools.keys()]\n        \n    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"调用工具\"\"\"\n        if tool_name not in self.tools:\n            return {\n                \"success\": False,\n                \"error\": f\"Tool {tool_name} not found\",\n                \"result\": None\n            }\n            \n        tool = self.tools[tool_name]\n        \n        try:\n            # 验证参数\n            validation_result = self._validate_parameters(tool, parameters)\n            if not validation_result[\"valid\"]:\n                return {\n                    \"success\": False,\n                    \"error\": validation_result[\"error\"],\n                    \"result\": None\n                }\n                \n            # 调用工具函数\n            if tool.async_function:\n                result = await tool.function(**parameters)\n            else:\n                result = tool.function(**parameters)\n                \n            return {\n                \"success\": True,\n                \"error\": None,\n                \"result\": result\n            }\n            \n        except Exception as e:\n            self.logger.error(f\"Tool {tool_name} execution failed: {e}\")\n            return {\n                \"success\": False,\n                \"error\": str(e),\n                \"result\": None\n            }\n            \n    def _validate_parameters(self, tool: ToolDefinition, parameters: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"验证参数\"\"\"\n        # 检查必需参数\n        for param in tool.parameters:\n            if param.required and param.name not in parameters:\n                return {\n                    \"valid\": False,\n                    \"error\": f\"Required parameter '{param.name}' missing\"\n                }\n                \n        # 检查参数类型（简单验证）\n        for param_name, param_value in parameters.items():\n            param_def = next((p for p in tool.parameters if p.name == param_name), None)\n            if param_def:\n                if not self._check_type(param_value, param_def.type):\n                    return {\n                        \"valid\": False,\n                        \"error\": f\"Parameter '{param_name}' type mismatch, expected {param_def.type}\"\n                    }\n                    \n        return {\"valid\": True, \"error\": None}\n        \n    def _check_type(self, value: Any, expected_type: str) -> bool:\n        \"\"\"检查参数类型\"\"\"\n        type_mapping = {\n            \"string\": str,\n            \"integer\": int,\n            \"boolean\": bool,\n            \"array\": list,\n            \"object\": dict\n        }\n        \n        expected_python_type = type_mapping.get(expected_type)\n        if expected_python_type:\n            return isinstance(value, expected_python_type)\n            \n        return True  # 未知类型暂时通过\n\n# ERA专用工具定义\nclass ERATools:\n    \"\"\"ERA游戏生成专用工具集\"\"\"\n    \n    def __init__(self, db, rag_system):\n        self.db = db\n        self.rag_system = rag_system\n        self.logger = logging.getLogger(__name__)\n        \n    def validate_erb_syntax(self, erb_code: str) -> Dict[str, Any]:\n        \"\"\"验证ERB语法\"\"\"\n        try:\n            # 简单的ERB语法检查\n            errors = []\n            warnings = []\n            \n            lines = erb_code.split('\\n')\n            for i, line in enumerate(lines, 1):\n                line = line.strip()\n                if not line or line.startswith(';'):\n                    continue\n                    \n                # 检查函数定义\n                if line.startswith('@'):\n                    if not line.endswith(')'):\n                        if '(' in line:\n                            errors.append(f\"Line {i}: Function definition missing closing parenthesis\")\n                    continue\n                    \n                # 检查变量定义\n                if line.startswith('#DIM') or line.startswith('#DIMS'):\n                    parts = line.split()\n                    if len(parts) < 2:\n                        errors.append(f\"Line {i}: Invalid variable definition\")\n                    continue\n                    \n                # 检查基本语法错误\n                if line.startswith('IF ') and not any(line.endswith(x) for x in ['THEN', 'ENDIF']):\n                    warnings.append(f\"Line {i}: IF statement without THEN or ENDIF\")\n                    \n            return {\n                \"valid\": len(errors) == 0,\n                \"errors\": errors,\n                \"warnings\": warnings,\n                \"line_count\": len(lines)\n            }\n            \n        except Exception as e:\n            return {\n                \"valid\": False,\n                \"errors\": [f\"Syntax validation failed: {str(e)}\"],\n                \"warnings\": [],\n                \"line_count\": 0\n            }\n            \n    def format_csv_data(self, data: List[List[str]], csv_type: str) -> str:\n        \"\"\"格式化CSV数据\"\"\"\n        try:\n            # 根据CSV类型添加头部注释\n            csv_headers = {\n                \"Chara\": \"角色数据文件\",\n                \"Abl\": \"能力定义文件\", \n                \"Talent\": \"素质定义文件\",\n                \"Train\": \"调教指令文件\",\n                \"Item\": \"道具定义文件\"\n            }\n            \n            result = []\n            \n            # 添加注释头\n            if csv_type in csv_headers:\n                result.append(f\";{csv_headers[csv_type]}\")\n                result.append(\";generated by ERA AI Agent\")\n                result.append(\"\")\n                \n            # 格式化数据行\n            for row in data:\n                # 确保每个字段都是字符串\n                formatted_row = [str(field) for field in row]\n                result.append(\",\".join(formatted_row))\n                \n            return \"\\n\".join(result)\n            \n        except Exception as e:\n            self.logger.error(f\"CSV formatting failed: {e}\")\n            return \"\"\n            \n    def generate_erb_function(self, function_name: str, \n                            function_type: str,\n                            parameters: List[str] = None) -> str:\n        \"\"\"生成ERB函数模板\"\"\"\n        try:\n            # 获取相关的ERA语法指导\n            guidance = self.rag_system.get_era_guidance(\"erb_function\", f\"如何实现{function_type}函数\")\n            \n            # 基本函数结构\n            result = []\n            \n            # 函数声明\n            if parameters:\n                param_str = \", \".join([f\"ARG:{i}\" for i in range(len(parameters))])\n                result.append(f\"@{function_name}({param_str})\")\n            else:\n                result.append(f\"@{function_name}\")\n                \n            result.append(\"#FUNCTION\")\n            result.append(\"\")\n            \n            # 添加注释\n            result.append(f\";{function_type}函数实现\")\n            result.append(\";TODO: 根据具体需求实现功能\")\n            result.append(\"\")\n            \n            # 根据函数类型添加基本结构\n            if function_type == \"SYSTEM_TITLE\":\n                result.extend([\n                    \"DRAWLINE\",\n                    \"PRINTL %GAME_TITLE%\",\n                    \"DRAWLINE\", \n                    \"PRINTL [0] 新的游戏\",\n                    \"PRINTL [1] 加载游戏\",\n                    \"INPUT\",\n                    \"SIF RESULT == 0\",\n                    \"\\tCALL EVENTFIRST\",\n                    \"ELSEIF RESULT == 1\",\n                    \"\\tCALL LOADGAME\",\n                    \"ENDIF\"\n                ])\n            elif function_type == \"TRAIN\":\n                result.extend([\n                    \"CALL SHOW_STATUS\",\n                    \"PRINTL 选择调教指令:\",\n                    \"CALL COMABLE\",\n                    \"INPUT\", \n                    \"CALL COM{RESULT}\"\n                ])\n            else:\n                result.append(\"RETURN 1\")\n                \n            result.append(\"\")\n            \n            return \"\\n\".join(result)\n            \n        except Exception as e:\n            self.logger.error(f\"ERB function generation failed: {e}\")\n            return f\"@{function_name}\\n#FUNCTION\\n;生成失败: {str(e)}\\nRETURN 0\\n\"\n            \n    def query_era_knowledge(self, question: str, category: str = \"general\") -> Dict[str, Any]:\n        \"\"\"查询ERA知识库\"\"\"\n        try:\n            result = self.rag_system.query(question, category)\n            \n            return {\n                \"question\": question,\n                \"category\": category,\n                \"context\": result.get(\"context\", \"\"),\n                \"results\": result.get(\"results\", []),\n                \"found_items\": len(result.get(\"results\", []))\n            }\n            \n        except Exception as e:\n            self.logger.error(f\"ERA knowledge query failed: {e}\")\n            return {\n                \"question\": question,\n                \"category\": category,\n                \"context\": \"查询失败\",\n                \"results\": [],\n                \"found_items\": 0,\n                \"error\": str(e)\n            }\n            \n    def create_character_data(self, character_info: Dict[str, Any]) -> List[str]:\n        \"\"\"创建角色数据\"\"\"\n        try:\n            # 构建角色CSV行数据\n            char_data = [\n                str(character_info.get(\"id\", 0)),\n                character_info.get(\"name\", \"未命名\"),\n                character_info.get(\"callname\", \"\"),\n                character_info.get(\"nickname\", \"\"),\n                \"\",  # 素质数据\n                \"\",  # 基础数据\n                \"\",  # 能力数据\n                \"\",  # 经验数据\n                \"\",  # 标志数据\n                \"\",  # 标记数据\n                \"\"   # 参数数据\n            ]\n            \n            return char_data\n            \n        except Exception as e:\n            self.logger.error(f\"Character data creation failed: {e}\")\n            return []\n            \n    async def save_generated_file(self, project_id: int, file_path: str, \n                                content: str, file_type: str) -> bool:\n        \"\"\"保存生成的文件\"\"\"\n        try:\n            # 保存到数据库\n            self.db.save_generated_file(project_id, file_path, file_type, content)\n            \n            # 保存到文件系统\n            import os\n            os.makedirs(os.path.dirname(file_path), exist_ok=True)\n            \n            with open(file_path, 'w', encoding='utf-8-sig') as f:\n                f.write(content)\n                \n            self.logger.info(f\"Saved file: {file_path}\")\n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"File save failed: {e}\")\n            return False\n\ndef register_era_tools(tool_manager: MCPToolManager, era_tools: ERATools):\n    \"\"\"注册ERA专用工具\"\"\"\n    \n    # ERB语法验证工具\n    tool_manager.register_tool(\n        name=\"validate_erb_syntax\",\n        description=\"验证ERB脚本语法\",\n        category=ToolType.VALIDATION,\n        parameters=[\n            ToolParameter(\"erb_code\", \"string\", \"ERB代码字符串\")\n        ],\n        function=era_tools.validate_erb_syntax\n    )\n    \n    # CSV格式化工具\n    tool_manager.register_tool(\n        name=\"format_csv_data\",\n        description=\"格式化CSV数据\",\n        category=ToolType.DATA_QUERY,\n        parameters=[\n            ToolParameter(\"data\", \"array\", \"CSV数据行数组\"),\n            ToolParameter(\"csv_type\", \"string\", \"CSV类型\", enum=[\"Chara\", \"Abl\", \"Talent\", \"Train\", \"Item\"])\n        ],\n        function=era_tools.format_csv_data\n    )\n    \n    # ERB函数生成工具\n    tool_manager.register_tool(\n        name=\"generate_erb_function\",\n        description=\"生成ERB函数模板\",\n        category=ToolType.GENERATION,\n        parameters=[\n            ToolParameter(\"function_name\", \"string\", \"函数名称\"),\n            ToolParameter(\"function_type\", \"string\", \"函数类型\"),\n            ToolParameter(\"parameters\", \"array\", \"函数参数列表\", required=False)\n        ],\n        function=era_tools.generate_erb_function\n    )\n    \n    # ERA知识查询工具\n    tool_manager.register_tool(\n        name=\"query_era_knowledge\", \n        description=\"查询ERA语法知识库\",\n        category=ToolType.DATA_QUERY,\n        parameters=[\n            ToolParameter(\"question\", \"string\", \"查询问题\"),\n            ToolParameter(\"category\", \"string\", \"知识类别\", required=False, default=\"general\")\n        ],\n        function=era_tools.query_era_knowledge\n    )\n    \n    # 角色数据创建工具\n    tool_manager.register_tool(\n        name=\"create_character_data\",\n        description=\"创建角色CSV数据\",\n        category=ToolType.GENERATION,\n        parameters=[\n            ToolParameter(\"character_info\", \"object\", \"角色信息字典\")\n        ],\n        function=era_tools.create_character_data\n    )\n    \n    # 文件保存工具\n    tool_manager.register_tool(\n        name=\"save_generated_file\",\n        description=\"保存生成的文件\",\n        category=ToolType.FILE_OPERATION,\n        parameters=[\n            ToolParameter(\"project_id\", \"integer\", \"项目ID\"),\n            ToolParameter(\"file_path\", \"string\", \"文件路径\"),\n            ToolParameter(\"content\", \"string\", \"文件内容\"),\n            ToolParameter(\"file_type\", \"string\", \"文件类型\")\n        ],\n        function=era_tools.save_generated_file,\n        async_function=True\n    )\n\n# 全局工具管理器实例\n_tool_manager = None\n_era_tools = None\n\ndef get_tool_manager() -> MCPToolManager:\n    \"\"\"获取工具管理器实例\"\"\"\n    global _tool_manager\n    if _tool_manager is None:\n        _tool_manager = MCPToolManager()\n    return _tool_manager\n    \ndef initialize_mcp_tools(db, rag_system) -> MCPToolManager:\n    \"\"\"初始化MCP工具\"\"\"\n    global _tool_manager, _era_tools\n    \n    _tool_manager = MCPToolManager()\n    _era_tools = ERATools(db, rag_system)\n    \n    # 注册ERA专用工具\n    register_era_tools(_tool_manager, _era_tools)\n    \n    logging.info(f\"Initialized MCP tools: {len(_tool_manager.tools)} tools registered\")\n    return _tool_manager"