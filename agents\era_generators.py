"""
ERA游戏生成器代理模块
包含各种专门的代理类，用于生成ERA游戏的不同组件
"""

import logging
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

from .base_agent import BaseERAAgent
from ..models.game_config import GameConfig


class ERBScriptAgent(BaseERAAgent):
    """ERB脚本生成代理"""
    
    def __init__(self):
        super().__init__("erb_script_agent", "erb_script")
        
    async def generate_system_erb(self, game_config: GameConfig) -> str:
        """生成SYSTEM.ERB文件"""
        prompt = f"""生成ERA游戏的SYSTEM.ERB文件，这是游戏的核心系统文件。

游戏配置：
- 游戏名称：{game_config.game_name}
- 游戏描述：{game_config.game_description}
- 角色数量：{game_config.character_count}

要求：
1. 包含基本的系统函数定义
2. 实现游戏初始化逻辑
3. 包含必要的变量声明
4. 添加适当的注释

请生成完整的SYSTEM.ERB代码。"""
        
        guidance = self.rag_system.get_era_guidance("erb_syntax", "SYSTEM文件结构")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)
        return content
        
    async def generate_title_erb(self, game_config: GameConfig) -> str:
        """生成TITLE.ERB文件"""
        prompt = f"""生成ERA游戏的TITLE.ERB文件，这是游戏的标题画面文件。

游戏配置：
- 游戏名称：{game_config.game_name}
- 游戏描述：{game_config.game_description}

要求：
1. 实现@TITLE函数
2. 显示游戏标题和菜单
3. 处理用户输入
4. 包含游戏开始逻辑

请生成完整的TITLE.ERB代码。"""
        
        guidance = self.rag_system.get_era_guidance("erb_syntax", "TITLE画面实现")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)
        return content
        
    async def generate_variables_erh(self, game_config: GameConfig) -> str:
        """生成VARIABLES.ERH文件"""
        prompt = f"""生成ERA游戏的VARIABLES.ERH文件，定义游戏中使用的变量。

游戏配置：
- 游戏特性：{', '.join(game_config.features)}
- 角色数量：{game_config.character_count}

要求：
1. 定义角色相关变量
2. 定义游戏状态变量
3. 定义系统变量
4. 添加变量说明注释

请生成完整的VARIABLES.ERH代码。"""
        
        guidance = self.rag_system.get_era_guidance("erb_syntax", "变量定义")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)
        return content
        
    async def generate_train_main_erb(self, game_config: GameConfig) -> str:
        """生成TRAIN_MAIN.ERB文件"""
        prompt = f"""生成ERA游戏的TRAIN_MAIN.ERB文件，这是主要的调教/训练界面。

游戏配置：
- 游戏类型：{game_config.game_description}
- 特性：{', '.join(game_config.features)}

要求：
1. 实现@TRAIN函数
2. 显示角色状态
3. 提供调教选项菜单
4. 处理调教指令的调用

请生成完整的TRAIN_MAIN.ERB代码。"""
        
        guidance = self.rag_system.get_era_guidance("game_flow", "TRAIN流程实现")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)
        return content


class CSVDataAgent(BaseERAAgent):
    """CSV数据生成代理"""
    
    def __init__(self):
        super().__init__("csv_data_agent", "csv_data")
        
    async def generate_character_csv(self, character_info: Dict[str, Any]) -> str:
        """生成角色CSV文件"""
        try:
            # 使用工具创建角色数据
            char_data_result = await self.tool_manager.call_tool(
                "create_character_data", 
                {"character_info": character_info}
            )
            
            if char_data_result["success"]:
                char_data = char_data_result["result"]
                
                # 格式化为CSV
                csv_result = await self.tool_manager.call_tool(
                    "format_csv_data",
                    {"data": [char_data], "csv_type": "Chara"}
                )
                
                if csv_result["success"]:
                    return csv_result["result"]
                    
            # 如果工具调用失败，手动生成
            return self._generate_character_csv_manual(character_info)
            
        except Exception as e:
            self.logger.error(f"CSV generation failed: {e}")
            return self._generate_character_csv_manual(character_info)
            
    def _generate_character_csv_manual(self, character_info: Dict[str, Any]) -> str:
        """手动生成角色CSV"""
        lines = [
            ";角色数据文件",
            ";generated by ERA AI Agent",
            "",
            f"{character_info.get('id', 0)},{character_info.get('name', '未命名')},{character_info.get('callname', '')},{character_info.get('nickname', '')}"
        ]
        return "\n".join(lines)
        
    async def generate_abl_csv(self, abilities: List[Dict[str, Any]]) -> str:
        """生成能力定义CSV"""
        prompt = """生成ERA游戏的Abl.csv文件，定义游戏中的各种能力。
        
要求：
1. 包含基础能力定义
2. 每行格式：编号,名称,表示名,说明
3. 添加必要的注释

请生成完整的Abl.csv内容。"""
        
        context = {"abilities": abilities}
        content = await self.generate_content(prompt, context)
        
        return content
        
    async def generate_talent_csv(self, talents: List[Dict[str, Any]]) -> str:
        """生成素质定义CSV"""
        prompt = """生成ERA游戏的Talent.csv文件，定义角色素质。
        
要求：
1. 包含各种角色素质
2. 每行格式：编号,名称,说明
3. 素质要有层次和分类

请生成完整的Talent.csv内容。"""
        
        context = {"talents": talents}
        content = await self.generate_content(prompt, context)
        
        return content
        
    async def generate_train_csv(self, train_commands: List[Dict[str, Any]]) -> str:
        """生成训练指令CSV"""
        prompt = """生成ERA游戏的Train.csv文件，定义训练/调教指令。
        
要求：
1. 包含各种调教指令
2. 每行格式：编号,名称,说明,条件
3. 指令要分类合理

请生成完整的Train.csv内容。"""
        
        context = {"train_commands": train_commands}
        content = await self.generate_content(prompt, context)
        
        return content


class SystemFlowAgent(BaseERAAgent):
    """系统流程代理"""
    
    def __init__(self):
        super().__init__("system_flow_agent", "system_flow")
        
    async def implement_game_flows(self, game_config: GameConfig) -> Dict[str, str]:
        """实现游戏流程"""
        flows = {}
        
        # 实现各个系统流程
        for flow_name in ["EVENTFIRST", "SHOP", "ABLUP", "AFTERTRAIN", "TURNEND"]:
            flow_content = await self._generate_flow_function(flow_name, game_config)
            flows[flow_name] = flow_content
            
        return flows
        
    async def _generate_flow_function(self, flow_name: str, game_config: GameConfig) -> str:
        """生成流程函数"""
        prompt = f"""生成ERA游戏的{flow_name}流程函数。
        
请根据ERA游戏引擎的规范，实现{flow_name}函数的完整逻辑。
包含必要的注释和错误处理。"""
        
        guidance = self.rag_system.get_era_guidance("game_flow", f"{flow_name}流程")
        prompt += f"\n\nERA语法参考：\n{guidance}"
        
        content = await self.generate_content(prompt, game_config.__dict__)
        return content


class CharacterAgent(BaseERAAgent):
    """角色生成代理"""
    
    def __init__(self):
        super().__init__("character_agent", "character")
        
    async def create_characters(self, character_count: int, game_theme: str) -> List[Dict[str, Any]]:
        """创建游戏角色"""
        characters = []
        
        prompt = f"""为ERA游戏创建{character_count}个角色。游戏主题：{game_theme}
        
要求：
1. 每个角色要有独特的姓名、称呼和昵称
2. 角色要符合游戏主题
3. 提供角色的基本属性和背景
4. 返回JSON格式的角色信息

请生成角色列表。"""
        
        content = await self.generate_content(prompt)
        
        try:
            # 尝试解析JSON
            import re
            json_match = re.search(r'\[.*\]', content, re.DOTALL)
            if json_match:
                characters_data = json.loads(json_match.group())
                characters = characters_data
            else:
                # 如果无法解析JSON，手动创建角色
                for i in range(character_count):
                    characters.append({
                        "id": i,
                        "name": f"角色{i+1}",
                        "callname": f"角色{i+1}",
                        "nickname": "",
                        "description": f"由AI生成的角色{i+1}"
                    })
                    
        except Exception as e:
            self.logger.error(f"Character parsing failed: {e}")
            # 创建默认角色
            for i in range(character_count):
                characters.append({
                    "id": i,
                    "name": f"角色{i+1}",
                    "callname": f"角色{i+1}",
                    "nickname": "",
                    "description": f"默认角色{i+1}"
                })
                
        return characters


class GameLogicAgent(BaseERAAgent):
    """游戏逻辑代理"""

    def __init__(self):
        super().__init__("game_logic_agent", "game_logic")

    async def integrate_game_logic(self, features: List[str], generated_files: List[str]) -> Dict[str, Any]:
        """整合游戏逻辑"""
        integration_result = {
            "config_files": [],
            "erb_patches": [],
            "validation_results": []
        }

        # 生成配置文件
        config_content = await self._generate_config_files(features)
        integration_result["config_files"] = config_content

        # 验证生成的文件
        for file_path in generated_files:
            if file_path.endswith('.ERB'):
                # 验证ERB文件
                validation = await self._validate_erb_file(file_path)
                integration_result["validation_results"].append(validation)

        return integration_result

    async def _generate_config_files(self, features: List[str]) -> Dict[str, str]:
        """生成配置文件"""
        configs = {}

        # 生成默认配置
        default_config = await self._generate_default_config(features)
        configs["_default.config"] = default_config

        # 生成固定配置
        fixed_config = await self._generate_fixed_config()
        configs["_fixed.config"] = fixed_config

        # 生成替换配置
        replace_config = await self._generate_replace_config()
        configs["_replace.csv"] = replace_config

        return configs

    async def _generate_default_config(self, features: List[str]) -> str:
        """生成默认配置文件"""
        prompt = """生成ERA游戏的_default.config配置文件。

要求：
1. 包含游戏显示设置
2. 包含引擎配置参数
3. 根据游戏特性调整配置

请生成完整的配置内容。"""

        context = {"features": features}
        content = await self.generate_content(prompt, context)
        return content

    async def _generate_fixed_config(self) -> str:
        """生成固定配置文件"""
        return """#固定配置文件
#generated by ERA AI Agent

#基本设置
FONTNAME = MS Gothic
FONTSIZE = 16
LINEHEIGHT = 18

#窗口设置
WIDTH = 800
HEIGHT = 600
"""

    async def _generate_replace_config(self) -> str:
        """生成替换配置文件"""
        return """;替换配置文件
;generated by ERA AI Agent

;基本替换
%%GAME_TITLE%%,ERA游戏标题
%%VERSION%%,1.0.0
"""

    async def _validate_erb_file(self, file_path: str) -> Dict[str, Any]:
        """验证ERB文件"""
        try:
            # 这里应该读取实际文件内容进行验证
            # 为了演示，返回模拟结果
            return {
                "file_path": file_path,
                "valid": True,
                "errors": [],
                "warnings": []
            }
        except Exception as e:
            return {
                "file_path": file_path,
                "valid": False,
                "errors": [str(e)],
                "warnings": []
            }


class ERAGameGenerator:
    """ERA游戏生成器主类"""

    def __init__(self):
        self.agents = {
            "erb_script": ERBScriptAgent(),
            "csv_data": CSVDataAgent(),
            "system_flow": SystemFlowAgent(),
            "character": CharacterAgent(),
            "game_logic": GameLogicAgent()
        }
        self.logger = logging.getLogger(__name__)

    async def generate_complete_game(self, game_config: GameConfig) -> Dict[str, Any]:
        """生成完整的ERA游戏"""
        results = {
            "erb_files": {},
            "csv_files": {},
            "config_files": {},
            "characters": [],
            "validation_results": []
        }

        try:
            # 1. 生成ERB脚本文件
            erb_agent = self.agents["erb_script"]
            results["erb_files"]["SYSTEM.ERB"] = await erb_agent.generate_system_erb(game_config)
            results["erb_files"]["TITLE.ERB"] = await erb_agent.generate_title_erb(game_config)
            results["erb_files"]["VARIABLES.ERH"] = await erb_agent.generate_variables_erh(game_config)
            results["erb_files"]["TRAIN_MAIN.ERB"] = await erb_agent.generate_train_main_erb(game_config)

            # 2. 创建角色
            character_agent = self.agents["character"]
            characters = await character_agent.create_characters(
                game_config.character_count,
                game_config.game_description
            )
            results["characters"] = characters

            # 3. 生成CSV数据文件
            csv_agent = self.agents["csv_data"]

            # 生成角色CSV
            for i, char in enumerate(characters):
                char_csv = await csv_agent.generate_character_csv(char)
                results["csv_files"][f"Chara{i}.csv"] = char_csv

            # 生成其他CSV文件
            results["csv_files"]["Abl.csv"] = await csv_agent.generate_abl_csv([])
            results["csv_files"]["Talent.csv"] = await csv_agent.generate_talent_csv([])
            results["csv_files"]["Train.csv"] = await csv_agent.generate_train_csv([])

            # 4. 实现系统流程
            flow_agent = self.agents["system_flow"]
            flows = await flow_agent.implement_game_flows(game_config)
            for flow_name, flow_content in flows.items():
                results["erb_files"][f"{flow_name}.ERB"] = flow_content

            # 5. 整合游戏逻辑
            logic_agent = self.agents["game_logic"]
            generated_files = list(results["erb_files"].keys()) + list(results["csv_files"].keys())
            integration = await logic_agent.integrate_game_logic(
                game_config.features,
                generated_files
            )
            results["config_files"] = integration["config_files"]
            results["validation_results"] = integration["validation_results"]

            self.logger.info("ERA game generation completed successfully")

        except Exception as e:
            self.logger.error(f"Game generation failed: {e}")
            results["error"] = str(e)

        return results

    async def save_game_files(self, game_config: GameConfig,
                            generation_results: Dict[str, Any]) -> List[str]:
        """保存游戏文件到磁盘"""
        saved_files = []
        output_path = Path(game_config.output_path)

        try:
            # 创建目录结构
            (output_path / "ERB").mkdir(parents=True, exist_ok=True)
            (output_path / "CSV" / "Chara").mkdir(parents=True, exist_ok=True)
            (output_path / "resources").mkdir(parents=True, exist_ok=True)

            # 保存ERB文件
            for filename, content in generation_results["erb_files"].items():
                file_path = output_path / "ERB" / filename
                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 保存CSV文件
            for filename, content in generation_results["csv_files"].items():
                if filename.startswith("Chara"):
                    file_path = output_path / "CSV" / "Chara" / filename
                else:
                    file_path = output_path / "CSV" / filename

                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 保存配置文件
            for filename, content in generation_results["config_files"].items():
                file_path = output_path / filename
                encoding = 'utf-8-sig' if filename.endswith('.csv') else 'utf-8'
                with open(file_path, 'w', encoding=encoding) as f:
                    f.write(content)
                saved_files.append(str(file_path))

            # 创建基本的资源文件
            img_csv_path = output_path / "resources" / "img.csv"
            with open(img_csv_path, 'w', encoding='utf-8-sig') as f:
                f.write(";图片资源配置\n;generated by ERA AI Agent\n")
            saved_files.append(str(img_csv_path))

            self.logger.info(f"Saved {len(saved_files)} game files")

        except Exception as e:
            self.logger.error(f"Failed to save game files: {e}")

        return saved_files


# 全局生成器实例
_game_generator = None

def get_game_generator() -> ERAGameGenerator:
    """获取游戏生成器实例"""
    global _game_generator
    if _game_generator is None:
        _game_generator = ERAGameGenerator()
    return _game_generator
