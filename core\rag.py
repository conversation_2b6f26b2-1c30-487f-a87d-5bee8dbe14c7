"""
RAG系统实现 - 基于Ollama嵌入的ERA语法知识检索
"""

import os
import json
import logging
import asyncio
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import requests
from datetime import datetime

try:
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    import faiss
except ImportError:
    logging.warning("Optional dependencies missing. Install with: pip install numpy scikit-learn faiss-cpu")

from era_ai_agent.data.database import ERADatabase

class OllamaEmbedding:
    """Ollama嵌入服务客户端"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model = model or os.getenv("OLLAMA_EMBEDDING_MODEL", "nomic-embed-text")
        self.logger = logging.getLogger(__name__)
        
    async def get_embedding(self, text: str) -> List[float]:\n        \"\"\"获取文本嵌入向量\"\"\"\n        try:\n            response = requests.post(\n                f\"{self.base_url}/api/embeddings\",\n                json={\n                    \"model\": self.model,\n                    \"prompt\": text\n                },\n                timeout=30\n            )\n            \n            if response.status_code == 200:\n                result = response.json()\n                if \"embedding\" in result:\n                    return result[\"embedding\"]\n                else:\n                    self.logger.error(f\"No embedding in response: {result}\")\n                    return None\n            else:\n                self.logger.error(f\"Embedding request failed: {response.status_code} - {response.text}\")\n                return None\n                \n        except Exception as e:\n            self.logger.error(f\"Failed to get embedding: {e}\")\n            return None\n            \n    def get_embedding_sync(self, text: str) -> List[float]:\n        \"\"\"同步获取文本嵌入向量\"\"\"\n        try:\n            response = requests.post(\n                f\"{self.base_url}/api/embeddings\",\n                json={\n                    \"model\": self.model,\n                    \"prompt\": text\n                },\n                timeout=30\n            )\n            \n            if response.status_code == 200:\n                result = response.json()\n                if \"embedding\" in result:\n                    return result[\"embedding\"]\n                else:\n                    self.logger.error(f\"No embedding in response: {result}\")\n                    return None\n            else:\n                self.logger.error(f\"Embedding request failed: {response.status_code} - {response.text}\")\n                return None\n                \n        except Exception as e:\n            self.logger.error(f\"Failed to get embedding: {e}\")\n            return None\n            \n    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:\n        \"\"\"批量获取嵌入向量\"\"\"\n        embeddings = []\n        \n        for text in texts:\n            embedding = await self.get_embedding(text)\n            if embedding:\n                embeddings.append(embedding)\n            else:\n                # 如果某个文本嵌入失败，用零向量填充\n                embeddings.append([0.0] * 768)  # 假设768维\n                \n        return embeddings\n        \n    def test_connection(self) -> bool:\n        \"\"\"测试Ollama连接\"\"\"\n        try:\n            response = requests.get(f\"{self.base_url}/api/tags\", timeout=10)\n            if response.status_code == 200:\n                models = response.json().get(\"models\", [])\n                model_names = [model[\"name\"] for model in models]\n                \n                if self.model in model_names:\n                    self.logger.info(f\"Ollama connection successful, embedding model {self.model} available\")\n                    return True\n                else:\n                    self.logger.warning(f\"Embedding model {self.model} not found. Available models: {model_names}\")\n                    return False\n            else:\n                self.logger.error(f\"Failed to connect to Ollama: {response.status_code}\")\n                return False\n                \n        except Exception as e:\n            self.logger.error(f\"Ollama connection test failed: {e}\")\n            return False\n\nclass ERAKnowledgeBase:\n    \"\"\"ERA语法知识库管理\"\"\"\n    \n    def __init__(self, db: ERADatabase, embedding_client: OllamaEmbedding):\n        self.db = db\n        self.embedding_client = embedding_client\n        self.logger = logging.getLogger(__name__)\n        self.index = None  # FAISS索引\n        self.knowledge_items = []  # 知识条目\n        \n    def load_era_syntax_data(self, syntax_data_path: str):\n        \"\"\"加载ERA语法资料\"\"\"\n        syntax_path = Path(syntax_data_path)\n        \n        if not syntax_path.exists():\n            self.logger.error(f\"ERA syntax data path not found: {syntax_data_path}\")\n            return\n            \n        # 遍历所有.txt文件\n        for txt_file in syntax_path.glob(\"*.txt\"):\n            try:\n                with open(txt_file, 'r', encoding='utf-8') as f:\n                    content = f.read()\n                    \n                # 基于文件名确定类别\n                category = self._categorize_file(txt_file.name)\n                \n                # 分段处理长文本\n                chunks = self._chunk_text(content)\n                \n                for i, chunk in enumerate(chunks):\n                    title = f\"{txt_file.stem}_{i+1}\" if len(chunks) > 1 else txt_file.stem\n                    \n                    # 生成嵌入向量\n                    embedding = self.embedding_client.get_embedding_sync(chunk)\n                    \n                    if embedding:\n                        # 保存到数据库\n                        embedding_bytes = np.array(embedding, dtype=np.float32).tobytes()\n                        self.db.save_era_knowledge(\n                            category=category,\n                            title=title,\n                            content=chunk,\n                            embedding=embedding_bytes,\n                            metadata={\n                                \"file_name\": txt_file.name,\n                                \"chunk_index\": i,\n                                \"chunk_count\": len(chunks)\n                            }\n                        )\n                        \n                self.logger.info(f\"Processed {txt_file.name}: {len(chunks)} chunks\")\n                \n            except Exception as e:\n                self.logger.error(f\"Failed to process {txt_file}: {e}\")\n                \n    def _categorize_file(self, filename: str) -> str:\n        \"\"\"根据文件名分类\"\"\"\n        filename_lower = filename.lower()\n        \n        if \"erh\" in filename_lower:\n            return \"variables\"\n        elif \"excom\" in filename_lower:\n            return \"commands\"\n        elif \"exfunc\" in filename_lower:\n            return \"functions\"\n        elif \"config\" in filename_lower:\n            return \"configuration\"\n        elif \"构文\" in filename_lower or \"语法\" in filename_lower:\n            return \"syntax\"\n        elif \"游戏\" in filename_lower:\n            return \"game_design\"\n        elif \"教程\" in filename_lower:\n            return \"tutorial\"\n        else:\n            return \"general\"\n            \n    def _chunk_text(self, text: str, max_length: int = 1000) -> List[str]:\n        \"\"\"将长文本分块\"\"\"\n        if len(text) <= max_length:\n            return [text]\n            \n        chunks = []\n        lines = text.split('\\n')\n        current_chunk = \"\"\n        \n        for line in lines:\n            if len(current_chunk) + len(line) + 1 <= max_length:\n                current_chunk += line + \"\\n\"\n            else:\n                if current_chunk.strip():\n                    chunks.append(current_chunk.strip())\n                current_chunk = line + \"\\n\"\n                \n        if current_chunk.strip():\n            chunks.append(current_chunk.strip())\n            \n        return chunks\n        \n    def build_index(self):\n        \"\"\"构建FAISS向量索引\"\"\"\n        try:\n            # 从数据库获取所有知识条目\n            knowledge_items = self.db.search_era_knowledge()\n            \n            if not knowledge_items:\n                self.logger.warning(\"No knowledge items found in database\")\n                return\n                \n            embeddings = []\n            self.knowledge_items = []\n            \n            for item in knowledge_items:\n                # 解析嵌入向量\n                if item.get(\"embedding\"):\n                    # 从数据库获取嵌入向量（需要直接查询BLOB字段）\n                    embedding_data = self._get_embedding_from_db(item[\"id\"])\n                    if embedding_data:\n                        embedding = np.frombuffer(embedding_data, dtype=np.float32)\n                        embeddings.append(embedding)\n                        self.knowledge_items.append(item)\n                        \n            if embeddings:\n                # 构建FAISS索引\n                embeddings_matrix = np.vstack(embeddings).astype('float32')\n                dimension = embeddings_matrix.shape[1]\n                \n                self.index = faiss.IndexFlatIP(dimension)  # 内积索引\n                faiss.normalize_L2(embeddings_matrix)  # L2归一化\n                self.index.add(embeddings_matrix)\n                \n                self.logger.info(f\"Built FAISS index with {len(embeddings)} knowledge items\")\n                \n        except Exception as e:\n            self.logger.error(f\"Failed to build index: {e}\")\n            \n    def _get_embedding_from_db(self, knowledge_id: int) -> bytes:\n        \"\"\"从数据库获取嵌入向量\"\"\"\n        try:\n            import sqlite3\n            with sqlite3.connect(self.db.db_path) as conn:\n                cursor = conn.cursor()\n                cursor.execute(\"SELECT embedding FROM era_knowledge WHERE id = ?\", (knowledge_id,))\n                result = cursor.fetchone()\n                return result[0] if result else None\n        except Exception as e:\n            self.logger.error(f\"Failed to get embedding from db: {e}\")\n            return None\n            \n    def search(self, query: str, top_k: int = 5, category: Optional[str] = None) -> List[Dict[str, Any]]:\n        \"\"\"搜索相关知识\"\"\"\n        try:\n            # 获取查询嵌入\n            query_embedding = self.embedding_client.get_embedding_sync(query)\n            if not query_embedding:\n                self.logger.error(\"Failed to get query embedding\")\n                return []\n                \n            # 使用FAISS搜索\n            if self.index is None:\n                self.build_index()\n                \n            if self.index is None:\n                self.logger.error(\"Failed to build search index\")\n                return []\n                \n            query_vector = np.array([query_embedding], dtype=np.float32)\n            faiss.normalize_L2(query_vector)\n            \n            scores, indices = self.index.search(query_vector, min(top_k * 2, len(self.knowledge_items)))\n            \n            results = []\n            for score, idx in zip(scores[0], indices[0]):\n                if idx >= 0 and idx < len(self.knowledge_items):\n                    item = self.knowledge_items[idx].copy()\n                    item[\"similarity_score\"] = float(score)\n                    \n                    # 类别过滤\n                    if category is None or item.get(\"category\") == category:\n                        results.append(item)\n                        \n                    if len(results) >= top_k:\n                        break\n                        \n            return results\n            \n        except Exception as e:\n            self.logger.error(f\"Search failed: {e}\")\n            return []\n            \n    def get_relevant_context(self, query: str, context_type: str = \"general\") -> str:\n        \"\"\"获取相关上下文\"\"\"\n        # 根据上下文类型搜索\n        category_map = {\n            \"erb_syntax\": \"syntax\",\n            \"variables\": \"variables\", \n            \"commands\": \"commands\",\n            \"functions\": \"functions\",\n            \"game_design\": \"game_design\"\n        }\n        \n        category = category_map.get(context_type)\n        results = self.search(query, top_k=3, category=category)\n        \n        if not results:\n            return \"没有找到相关的ERA语法信息。\"\n            \n        context_parts = []\n        for i, result in enumerate(results, 1):\n            context_parts.append(f\"参考资料{i}:\\n{result['content']}\\n\")\n            \n        return \"\\n\".join(context_parts)\n\nclass ERARAGSystem:\n    \"\"\"ERA RAG系统主类\"\"\"\n    \n    def __init__(self, db_path: str = \"era_agent.db\", \n                 ollama_base_url: str = None,\n                 embedding_model: str = None):\n        self.db = ERADatabase(db_path)\n        self.embedding_client = OllamaEmbedding(ollama_base_url, embedding_model)\n        self.knowledge_base = ERAKnowledgeBase(self.db, self.embedding_client)\n        self.logger = logging.getLogger(__name__)\n        \n    def initialize(self, era_syntax_path: str):\n        \"\"\"初始化RAG系统\"\"\"\n        try:\n            # 初始化数据库\n            self.db.initialize_database()\n            \n            # 测试Ollama连接\n            if not self.embedding_client.test_connection():\n                self.logger.error(\"Failed to connect to Ollama\")\n                return False\n                \n            # 加载ERA语法数据\n            self.knowledge_base.load_era_syntax_data(era_syntax_path)\n            \n            # 构建索引\n            self.knowledge_base.build_index()\n            \n            self.logger.info(\"ERA RAG system initialized successfully\")\n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"Failed to initialize RAG system: {e}\")\n            return False\n            \n    def query(self, question: str, context_type: str = \"general\") -> Dict[str, Any]:\n        \"\"\"查询ERA相关信息\"\"\"\n        try:\n            # 搜索相关知识\n            results = self.knowledge_base.search(question, top_k=5)\n            \n            # 获取上下文\n            context = self.knowledge_base.get_relevant_context(question, context_type)\n            \n            return {\n                \"question\": question,\n                \"context\": context,\n                \"results\": results,\n                \"context_type\": context_type\n            }\n            \n        except Exception as e:\n            self.logger.error(f\"Query failed: {e}\")\n            return {\n                \"question\": question,\n                \"context\": \"查询失败，请检查系统状态。\",\n                \"results\": [],\n                \"context_type\": context_type,\n                \"error\": str(e)\n            }\n            \n    def get_era_guidance(self, task_type: str, specific_question: str = \"\") -> str:\n        \"\"\"获取ERA开发指导\"\"\"\n        task_queries = {\n            \"erb_function\": \"如何编写ERB函数 系统函数定义\",\n            \"csv_data\": \"CSV文件格式 角色数据定义\",\n            \"game_flow\": \"游戏流程 系统流程 TITLE SHOP TRAIN\",\n            \"variables\": \"变量定义 FLAG ABL TALENT\",\n            \"commands\": \"ERA指令 PRINT INPUT\",\n            \"character\": \"角色定义 角色属性 BASE CFLAG\"\n        }\n        \n        query = task_queries.get(task_type, specific_question)\n        if not query:\n            query = specific_question\n            \n        result = self.query(query, task_type)\n        return result[\"context\"]\n\n# 全局RAG系统实例\n_rag_system = None\n\ndef get_rag_system() -> ERARAGSystem:\n    \"\"\"获取RAG系统实例\"\"\"\n    global _rag_system\n    if _rag_system is None:\n        _rag_system = ERARAGSystem()\n    return _rag_system\n    \ndef initialize_rag(era_syntax_path: str) -> bool:\n    \"\"\"初始化RAG系统\"\"\"\n    try:\n        rag = get_rag_system()\n        return rag.initialize(era_syntax_path)\n    except Exception as e:\n        logging.error(f\"Failed to initialize RAG: {e}\")\n        return False"