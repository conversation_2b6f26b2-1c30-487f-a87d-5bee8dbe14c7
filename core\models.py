"""
LM Studio本地模型接入配置
使用litelm提供统一的OpenAI兼容接口
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass
import asyncio
from pathlib import Path

try:
    from litellm import completion, acompletion, embedding
    import litellm
except ImportError:
    logging.error("litellm not installed. Please install with: pip install litellm")
    raise

@dataclass
class ModelConfig:
    """模型配置"""
    model_name: str
    api_base: str
    api_key: str = "not-needed"
    model_type: str = "openai"  # openai, anthropic, cohere等
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    streaming: bool = True

class LMStudioClient:
    """LM Studio客户端"""
    
    def __init__(self, config_path: str = ".env"):
        self.logger = logging.getLogger(__name__)
        self.models = {}
        self.default_model = None
        self.load_config(config_path)
        
    def load_config(self, config_path: str):
        """从环境变量或配置文件加载配置"""
        # 从环境变量加载
        lm_studio_base = os.getenv("LM_STUDIO_BASE_URL", "http://localhost:1234/v1")
        lm_studio_model = os.getenv("LM_STUDIO_MODEL", "local-model")
        
        # 如果有配置文件，从文件加载
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('\"').strip(\"'\")\
                        \n                        if key == \"LM_STUDIO_BASE_URL\":\n                            lm_studio_base = value\n                        elif key == \"LM_STUDIO_MODEL\":\n                            lm_studio_model = value\n                        elif key == \"OLLAMA_BASE_URL\":\n                            os.environ[\"OLLAMA_BASE_URL\"] = value\n                        elif key == \"OLLAMA_MODEL\":\n                            os.environ[\"OLLAMA_MODEL\"] = value\n                            \n            except Exception as e:\n                self.logger.warning(f\"Failed to load config from {config_path}: {e}\")\n                \n        # 配置默认模型\n        self.add_model(\n            \"lm_studio_default\",\n            ModelConfig(\n                model_name=lm_studio_model,\n                api_base=lm_studio_base,\n                api_key=\"not-needed\",\n                model_type=\"openai\"\n            )\n        )\n        \n        # 设置litellm配置\n        litellm.api_base = lm_studio_base\n        litellm.drop_params = True  # 忽略不支持的参数\n        litellm.set_verbose = False  # 关闭详细日志\n        \n        self.logger.info(f\"Configured LM Studio client with base URL: {lm_studio_base}\")\n        \n    def add_model(self, model_id: str, config: ModelConfig):\n        \"\"\"添加模型配置\"\"\"\n        self.models[model_id] = config\n        if not self.default_model:\n            self.default_model = model_id\n            \n    def get_model_config(self, model_id: Optional[str] = None) -> ModelConfig:\n        \"\"\"获取模型配置\"\"\"\n        if not model_id:\n            model_id = self.default_model\n            \n        if model_id not in self.models:\n            raise ValueError(f\"Model {model_id} not configured\")\n            \n        return self.models[model_id]\n        \n    async def chat_completion(self, \n                            messages: List[Dict[str, str]], \n                            model_id: Optional[str] = None,\n                            **kwargs) -> Dict[str, Any]:\n        \"\"\"异步聊天完成\"\"\"\n        config = self.get_model_config(model_id)\n        \n        try:\n            # 准备参数\n            params = {\n                \"model\": f\"openai/{config.model_name}\",  # 使用openai前缀\n                \"messages\": messages,\n                \"max_tokens\": kwargs.get(\"max_tokens\", config.max_tokens),\n                \"temperature\": kwargs.get(\"temperature\", config.temperature),\n                \"top_p\": kwargs.get(\"top_p\", config.top_p),\n                \"api_base\": config.api_base,\n                \"api_key\": config.api_key\n            }\n            \n            # 异步调用\n            response = await acompletion(**params)\n            \n            return {\n                \"content\": response.choices[0].message.content,\n                \"usage\": {\n                    \"prompt_tokens\": response.usage.prompt_tokens if response.usage else 0,\n                    \"completion_tokens\": response.usage.completion_tokens if response.usage else 0,\n                    \"total_tokens\": response.usage.total_tokens if response.usage else 0\n                },\n                \"model\": response.model,\n                \"finish_reason\": response.choices[0].finish_reason\n            }\n            \n        except Exception as e:\n            self.logger.error(f\"Chat completion failed: {e}\")\n            raise\n            \n    def chat_completion_sync(self, \n                           messages: List[Dict[str, str]], \n                           model_id: Optional[str] = None,\n                           **kwargs) -> Dict[str, Any]:\n        \"\"\"同步聊天完成\"\"\"\n        config = self.get_model_config(model_id)\n        \n        try:\n            params = {\n                \"model\": f\"openai/{config.model_name}\",\n                \"messages\": messages,\n                \"max_tokens\": kwargs.get(\"max_tokens\", config.max_tokens),\n                \"temperature\": kwargs.get(\"temperature\", config.temperature), \n                \"top_p\": kwargs.get(\"top_p\", config.top_p),\n                \"api_base\": config.api_base,\n                \"api_key\": config.api_key\n            }\n            \n            response = completion(**params)\n            \n            return {\n                \"content\": response.choices[0].message.content,\n                \"usage\": {\n                    \"prompt_tokens\": response.usage.prompt_tokens if response.usage else 0,\n                    \"completion_tokens\": response.usage.completion_tokens if response.usage else 0,\n                    \"total_tokens\": response.usage.total_tokens if response.usage else 0\n                },\n                \"model\": response.model,\n                \"finish_reason\": response.choices[0].finish_reason\n            }\n            \n        except Exception as e:\n            self.logger.error(f\"Sync chat completion failed: {e}\")\n            raise\n            \n    async def chat_completion_stream(self, \n                                   messages: List[Dict[str, str]], \n                                   model_id: Optional[str] = None,\n                                   **kwargs) -> AsyncGenerator[str, None]:\n        \"\"\"流式聊天完成\"\"\"\n        config = self.get_model_config(model_id)\n        \n        try:\n            params = {\n                \"model\": f\"openai/{config.model_name}\",\n                \"messages\": messages,\n                \"max_tokens\": kwargs.get(\"max_tokens\", config.max_tokens),\n                \"temperature\": kwargs.get(\"temperature\", config.temperature),\n                \"top_p\": kwargs.get(\"top_p\", config.top_p),\n                \"stream\": True,\n                \"api_base\": config.api_base,\n                \"api_key\": config.api_key\n            }\n            \n            response = await acompletion(**params)\n            \n            async for chunk in response:\n                if chunk.choices[0].delta.content:\n                    yield chunk.choices[0].delta.content\n                    \n        except Exception as e:\n            self.logger.error(f\"Stream completion failed: {e}\")\n            raise\n            \n    def test_connection(self, model_id: Optional[str] = None) -> bool:\n        \"\"\"测试模型连接\"\"\"\n        try:\n            messages = [{\"role\": \"user\", \"content\": \"Hello, please respond with 'OK' if you can understand this message.\"}]\n            response = self.chat_completion_sync(messages, model_id)\n            \n            if response and \"content\" in response:\n                self.logger.info(f\"Model connection test successful: {response['content'][:50]}...\")\n                return True\n            else:\n                self.logger.error(\"Model connection test failed: No valid response\")\n                return False\n                \n        except Exception as e:\n            self.logger.error(f\"Model connection test failed: {e}\")\n            return False\n            \n    def get_available_models(self) -> List[str]:\n        \"\"\"获取可用模型列表\"\"\"\n        return list(self.models.keys())\n        \n    def get_model_info(self, model_id: Optional[str] = None) -> Dict[str, Any]:\n        \"\"\"获取模型信息\"\"\"\n        config = self.get_model_config(model_id)\n        \n        return {\n            \"model_name\": config.model_name,\n            \"api_base\": config.api_base,\n            \"model_type\": config.model_type,\n            \"max_tokens\": config.max_tokens,\n            \"temperature\": config.temperature,\n            \"top_p\": config.top_p,\n            \"streaming\": config.streaming\n        }\n        \nclass ERAModelManager:\n    \"\"\"ERA模型管理器 - 为不同的Agent任务配置不同的模型\"\"\"\n    \n    def __init__(self, lm_client: LMStudioClient):\n        self.lm_client = lm_client\n        self.agent_models = {}\n        self.logger = logging.getLogger(__name__)\n        \n    def assign_model_to_agent(self, agent_type: str, model_id: str):\n        \"\"\"为代理分配模型\"\"\"\n        self.agent_models[agent_type] = model_id\n        self.logger.info(f\"Assigned model {model_id} to agent {agent_type}\")\n        \n    def get_agent_model(self, agent_type: str) -> str:\n        \"\"\"获取代理使用的模型\"\"\"\n        return self.agent_models.get(agent_type, self.lm_client.default_model)\n        \n    async def generate_for_agent(self, \n                               agent_type: str, \n                               prompt: str, \n                               system_prompt: Optional[str] = None,\n                               **kwargs) -> str:\n        \"\"\"为特定代理生成内容\"\"\"\n        model_id = self.get_agent_model(agent_type)\n        \n        messages = []\n        if system_prompt:\n            messages.append({\"role\": \"system\", \"content\": system_prompt})\n        messages.append({\"role\": \"user\", \"content\": prompt})\n        \n        response = await self.lm_client.chat_completion(messages, model_id, **kwargs)\n        return response[\"content\"]\n        \n    async def generate_with_context(self, \n                                  agent_type: str,\n                                  context: List[Dict[str, str]],\n                                  new_message: str,\n                                  **kwargs) -> str:\n        \"\"\"基于上下文生成内容\"\"\"\n        model_id = self.get_agent_model(agent_type)\n        \n        messages = context.copy()\n        messages.append({\"role\": \"user\", \"content\": new_message})\n        \n        response = await self.lm_client.chat_completion(messages, model_id, **kwargs)\n        return response[\"content\"]\n        \n    def get_agent_assignments(self) -> Dict[str, str]:\n        \"\"\"获取代理模型分配\"\"\"\n        return self.agent_models.copy()\n        \n# 全局模型客户端实例\n_lm_client = None\n_model_manager = None\n\ndef get_lm_client() -> LMStudioClient:\n    \"\"\"获取LM Studio客户端实例\"\"\"\n    global _lm_client\n    if _lm_client is None:\n        _lm_client = LMStudioClient()\n    return _lm_client\n    \ndef get_model_manager() -> ERAModelManager:\n    \"\"\"获取模型管理器实例\"\"\"\n    global _model_manager\n    if _model_manager is None:\n        _model_manager = ERAModelManager(get_lm_client())\n    return _model_manager\n    \ndef initialize_models(config_path: str = \".env\") -> bool:\n    \"\"\"初始化模型配置\"\"\"\n    try:\n        global _lm_client, _model_manager\n        \n        _lm_client = LMStudioClient(config_path)\n        _model_manager = ERAModelManager(_lm_client)\n        \n        # 测试连接\n        if _lm_client.test_connection():\n            logging.info(\"LM Studio models initialized successfully\")\n            return True\n        else:\n            logging.error(\"Failed to connect to LM Studio\")\n            return False\n            \n    except Exception as e:\n        logging.error(f\"Failed to initialize models: {e}\")\n        return False"