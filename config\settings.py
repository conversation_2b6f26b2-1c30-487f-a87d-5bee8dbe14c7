"""
系统配置管理
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

@dataclass
class SystemConfig:
    """系统配置"""
    # LM Studio配置
    lm_studio_base_url: str = "http://localhost:1234/v1"
    lm_studio_model: str = "local-model"
    
    # Ollama配置
    ollama_base_url: str = "http://localhost:11434"
    ollama_embedding_model: str = "nomic-embed-text"
    
    # 数据库配置
    database_path: str = "era_agent.db"
    
    # ERA语法数据路径
    era_syntax_data_path: str = ""
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "era_agent.log"
    
    # 生成配置
    default_max_tokens: int = 4096
    default_temperature: float = 0.7
    default_top_p: float = 0.9
    
    # 输出配置
    default_output_path: str = "./generated_games"
    default_encoding: str = "utf-8-sig"
    
    # 功能开关
    enable_rag: bool = True
    enable_mcp_tools: bool = True
    enable_validation: bool = True
    
    # 性能配置
    max_concurrent_agents: int = 3
    agent_timeout: int = 300
    rag_top_k: int = 5
    embedding_batch_size: int = 10
    
    # 开发配置
    debug_mode: bool = False
    save_intermediate_results: bool = True
    enable_cache: bool = True

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = ".env"):
        self.config_path = config_path
        self.config = SystemConfig()
        self.load_config()
        self.setup_logging()
        
    def load_config(self):
        """加载配置"""
        # 加载.env文件
        if os.path.exists(self.config_path):
            load_dotenv(self.config_path)
            
        # 从环境变量加载配置
        self.config.lm_studio_base_url = os.getenv("LM_STUDIO_BASE_URL", self.config.lm_studio_base_url)
        self.config.lm_studio_model = os.getenv("LM_STUDIO_MODEL", self.config.lm_studio_model)
        
        self.config.ollama_base_url = os.getenv("OLLAMA_BASE_URL", self.config.ollama_base_url)
        self.config.ollama_embedding_model = os.getenv("OLLAMA_EMBEDDING_MODEL", self.config.ollama_embedding_model)
        
        self.config.database_path = os.getenv("DATABASE_PATH", self.config.database_path)
        self.config.era_syntax_data_path = os.getenv("ERA_SYNTAX_DATA_PATH", self.config.era_syntax_data_path)
        
        self.config.log_level = os.getenv("LOG_LEVEL", self.config.log_level)
        self.config.log_file = os.getenv("LOG_FILE", self.config.log_file)
        
        self.config.default_max_tokens = int(os.getenv("DEFAULT_MAX_TOKENS", str(self.config.default_max_tokens)))
        self.config.default_temperature = float(os.getenv("DEFAULT_TEMPERATURE", str(self.config.default_temperature)))
        self.config.default_top_p = float(os.getenv("DEFAULT_TOP_P", str(self.config.default_top_p)))
        
        self.config.default_output_path = os.getenv("DEFAULT_OUTPUT_PATH", self.config.default_output_path)
        self.config.default_encoding = os.getenv("DEFAULT_ENCODING", self.config.default_encoding)
        
        self.config.enable_rag = os.getenv("ENABLE_RAG", "true").lower() == "true"
        self.config.enable_mcp_tools = os.getenv("ENABLE_MCP_TOOLS", "true").lower() == "true"
        self.config.enable_validation = os.getenv("ENABLE_VALIDATION", "true").lower() == "true"
        
        self.config.max_concurrent_agents = int(os.getenv("MAX_CONCURRENT_AGENTS", str(self.config.max_concurrent_agents)))
        self.config.agent_timeout = int(os.getenv("AGENT_TIMEOUT", str(self.config.agent_timeout)))
        self.config.rag_top_k = int(os.getenv("RAG_TOP_K", str(self.config.rag_top_k)))
        self.config.embedding_batch_size = int(os.getenv("EMBEDDING_BATCH_SIZE", str(self.config.embedding_batch_size)))
        
        self.config.debug_mode = os.getenv("DEBUG_MODE", "false").lower() == "true"
        self.config.save_intermediate_results = os.getenv("SAVE_INTERMEDIATE_RESULTS", "true").lower() == "true"
        self.config.enable_cache = os.getenv("ENABLE_CACHE", "true").lower() == "true"
        
    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        
        # 创建日志目录
        log_path = Path(self.config.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 配置日志格式
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # 配置日志处理器
        handlers = [
            logging.StreamHandler(),  # 控制台输出
            logging.FileHandler(self.config.log_file, encoding='utf-8')  # 文件输出
        ]
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=handlers,
            force=True
        )
        
        # 设置第三方库日志级别
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        
    def get_agent_model_config(self, agent_type: str) -> Dict[str, Any]:
        """获取代理的模型配置"""
        # 代理特定的模型配置（如果有的话）
        agent_model_key = f"{agent_type.upper()}_AGENT_MODEL"
        model_name = os.getenv(agent_model_key, self.config.lm_studio_model)
        
        return {
            "model_name": model_name,
            "api_base": self.config.lm_studio_base_url,
            "max_tokens": self.config.default_max_tokens,
            "temperature": self.config.default_temperature,
            "top_p": self.config.default_top_p
        }
        
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        warnings = []
        
        # 检查必需路径
        if not self.config.era_syntax_data_path:
            issues.append("ERA_SYNTAX_DATA_PATH not configured")
        elif not os.path.exists(self.config.era_syntax_data_path):
            issues.append(f"ERA syntax data path not found: {self.config.era_syntax_data_path}")
            
        # 检查输出目录
        try:
            Path(self.config.default_output_path).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            issues.append(f"Cannot create output directory: {e}")
            
        # 检查服务连接（可选）
        if self.config.enable_rag:
            # 这里可以添加Ollama连接测试
            pass
            
        # 检查LM Studio连接（可选）
        # 这里可以添加LM Studio连接测试
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings
        }
        
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "lm_studio": {
                "base_url": self.config.lm_studio_base_url,
                "model": self.config.lm_studio_model
            },
            "ollama": {
                "base_url": self.config.ollama_base_url,
                "embedding_model": self.config.ollama_embedding_model
            },
            "database": {
                "path": self.config.database_path
            },
            "era_data": {
                "path": self.config.era_syntax_data_path,
                "exists": os.path.exists(self.config.era_syntax_data_path) if self.config.era_syntax_data_path else False
            },
            "features": {
                "rag_enabled": self.config.enable_rag,
                "mcp_tools_enabled": self.config.enable_mcp_tools,
                "validation_enabled": self.config.enable_validation
            },
            "performance": {
                "max_concurrent_agents": self.config.max_concurrent_agents,
                "agent_timeout": self.config.agent_timeout,
                "rag_top_k": self.config.rag_top_k
            }
        }

# 全局配置管理器实例
_config_manager = None

def get_config() -> SystemConfig:
    """获取系统配置"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager.config

def get_config_manager() -> ConfigManager:
    """获取配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def initialize_config(config_path: str = ".env") -> bool:
    """初始化配置"""
    try:
        global _config_manager
        _config_manager = ConfigManager(config_path)
        
        # 验证配置
        validation = _config_manager.validate_config()
        
        if validation["valid"]:
            logging.info("Configuration initialized successfully")
            return True
        else:
            logging.error(f"Configuration validation failed: {validation['issues']}")
            return False
            
    except Exception as e:
        logging.error(f"Failed to initialize configuration: {e}")
        return False